import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

/// 简单网络测试 - 测试公告URL是否可访问
void main() async {
  print('🧪 简单网络测试开始...\n');
  
  final urls = [
    'https://www.dznovel.top/announcement.json',
    'http://47.120.19.139:8000/announcement.json',
  ];
  
  for (final url in urls) {
    print('🔗 测试: $url');
    
    try {
      final response = await http.get(Uri.parse(url)).timeout(Duration(seconds: 10));
      
      print('✅ 状态码: ${response.statusCode}');
      print('📏 内容长度: ${response.body.length}');
      
      if (response.statusCode == 200 && response.body.isNotEmpty) {
        print('📄 内容预览:');
        print(response.body.length > 200 ? 
          '${response.body.substring(0, 200)}...' : 
          response.body);
        
        // 尝试解析JSON
        try {
          final data = json.decode(response.body);
          print('✅ JSON解析成功');
          print('🔍 ID: ${data['id']}');
          print('🔍 标题: ${data['title']}');
          print('🔍 is_active: ${data['is_active']} (${data['is_active'].runtimeType})');
        } catch (e) {
          print('❌ JSON解析失败: $e');
        }
      } else {
        print('❌ 响应失败或内容为空');
      }
      
    } catch (e) {
      print('❌ 请求失败: $e');
    }
    
    print('\n' + '-' * 50 + '\n');
  }
  
  print('🏁 测试完成');
}
