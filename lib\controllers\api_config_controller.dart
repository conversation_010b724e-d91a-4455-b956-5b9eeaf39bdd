import 'dart:io';
import 'dart:convert';
import 'package:get/get.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:novel_app/models/embedding_model_config.dart';
import 'package:novel_app/services/embedding_service.dart';
import 'package:novel_app/utils/network_client.dart';

class ModelConfig {
  String name; // 模型名称
  String apiKey; // API密钥
  String apiUrl; // API地址
  String apiPath; // API路径
  String model; // 具体模型名称
  List<String> modelVariants; // 模型变体列表（多模型标识符）
  String apiFormat; // API格式（如OpenAI API兼容等）
  String appId; // 应用ID（百度千帆等需要）
  bool isCustom; // 是否为自定义模型
  double temperature;
  double topP;
  int maxTokens;
  double repetitionPenalty; // 添加重复惩罚参数
  bool useProxy; // 是否使用代理
  String proxyUrl; // 代理服务器地址
  int timeout; // 请求超时时间（秒）
  bool enableThinking; // 是否启用深度思考模式（阿里云通义千问特有）

  ModelConfig({
    required this.name,
    required this.apiKey,
    required this.apiUrl,
    required this.apiPath,
    required this.model,
    this.modelVariants = const [],
    required this.apiFormat,
    this.appId = '',
    this.isCustom = false,
    this.temperature = 0.7,
    this.topP = 1.0,
    this.maxTokens = 6000,
    this.repetitionPenalty = 1.3, // 设置默认值
    this.useProxy = false, // 默认不使用代理
    this.proxyUrl = '', // 默认代理地址为空
    this.timeout = 60, // 默认超时时间为60秒
    this.enableThinking = false, // 默认不启用深度思考模式
  });

  Map<String, dynamic> toJson() => {
        'name': name,
        'apiKey': apiKey,
        'apiUrl': apiUrl,
        'apiPath': apiPath,
        'model': model,
        'modelVariants': modelVariants,
        'apiFormat': apiFormat,
        'appId': appId,
        'isCustom': isCustom,
        'temperature': temperature,
        'topP': topP,
        'maxTokens': maxTokens,
        'repetitionPenalty': repetitionPenalty, // 添加到 JSON
        'useProxy': useProxy, // 添加代理设置
        'proxyUrl': proxyUrl, // 添加代理URL
        'timeout': timeout, // 添加超时时间
        'enableThinking': enableThinking, // 添加深度思考模式
      };

  factory ModelConfig.fromJson(Map<String, dynamic> json) => ModelConfig(
        name: json['name'] as String? ?? '',
        apiKey: json['apiKey'] as String? ?? '',
        apiUrl: json['apiUrl'] as String? ?? '',
        apiPath: json['apiPath'] as String? ?? '',
        model: json['model'] as String? ?? '',
        modelVariants: json['modelVariants'] != null
            ? List<String>.from(json['modelVariants'])
            : [],
        apiFormat: json['apiFormat'] as String? ?? 'OpenAI API兼容',
        appId: json['appId'] as String? ?? '',
        isCustom: json['isCustom'] as bool? ?? false,
        temperature: (json['temperature'] as num?)?.toDouble() ?? 0.7,
        topP: (json['topP'] as num?)?.toDouble() ?? 1.0,
        maxTokens: (json['maxTokens'] as num?)?.toInt() ?? 4000,
        repetitionPenalty:
            (json['repetitionPenalty'] as num?)?.toDouble() ?? 1.3, // 从 JSON 读取
        useProxy: json['useProxy'] as bool? ?? false, // 从 JSON 读取代理设置
        proxyUrl: json['proxyUrl'] as String? ?? '', // 从 JSON 读取代理URL
        timeout: (json['timeout'] as num?)?.toInt() ?? 60, // 从 JSON 读取超时时间
        enableThinking:
            json['enableThinking'] as bool? ?? false, // 从 JSON 读取深度思考模式
      );

  ModelConfig copyWith({
    String? name,
    String? apiKey,
    String? apiUrl,
    String? apiPath,
    String? model,
    List<String>? modelVariants,
    String? apiFormat,
    String? appId,
    bool? isCustom,
    double? temperature,
    double? topP,
    int? maxTokens,
    double? repetitionPenalty, // 添加到 copyWith
    bool? useProxy, // 添加代理设置
    String? proxyUrl, // 添加代理URL
    int? timeout, // 添加超时时间
    bool? enableThinking, // 添加深度思考模式
  }) {
    return ModelConfig(
      name: name ?? this.name,
      apiKey: apiKey ?? this.apiKey,
      apiUrl: apiUrl ?? this.apiUrl,
      apiPath: apiPath ?? this.apiPath,
      model: model ?? this.model,
      modelVariants: modelVariants ?? this.modelVariants,
      apiFormat: apiFormat ?? this.apiFormat,
      appId: appId ?? this.appId,
      isCustom: isCustom ?? this.isCustom,
      temperature: temperature ?? this.temperature,
      topP: topP ?? this.topP,
      maxTokens: maxTokens ?? this.maxTokens,
      repetitionPenalty: repetitionPenalty ?? this.repetitionPenalty, // 添加到构造
      useProxy: useProxy ?? this.useProxy, // 添加代理设置
      proxyUrl: proxyUrl ?? this.proxyUrl, // 添加代理URL
      timeout: timeout ?? this.timeout, // 添加超时时间
      enableThinking: enableThinking ?? this.enableThinking, // 添加深度思考模式
    );
  }

  // 添加模型变体
  void addModelVariant(String variant) {
    if (!modelVariants.contains(variant) && variant.isNotEmpty) {
      modelVariants.add(variant);
    }
  }

  // 删除模型变体
  void removeModelVariant(String variant) {
    modelVariants.remove(variant);
  }

  // 切换当前模型为指定变体
  void switchToVariant(String variant) {
    if (modelVariants.contains(variant)) {
      model = variant;
    }
  }
}

class ApiConfigController extends GetxController {
  static const _boxName = 'api_config';
  static const _customModelsKey = 'custom_models';
  static const _embeddingModelKey = 'embedding_model';
  static const _applyModelKey = 'apply_model';
  late final Box<dynamic> _box;

  final RxString selectedModelId = ''.obs;
  final RxList<ModelConfig> models = <ModelConfig>[].obs;
  final RxDouble temperature = 0.7.obs;
  final RxDouble topP = 1.0.obs;
  final RxInt maxTokens = 4000.obs;
  final RxDouble repetitionPenalty = 1.3.obs; // 添加重复惩罚参数的响应式变量

  // 嵌入模型配置
  final Rx<EmbeddingModelConfig> embeddingModel =
      EmbeddingModelConfig.getDefault().obs;

  // Apply模型配置（用于精确应用编辑建议）
  final Rx<ModelConfig?> applyModel = Rx<ModelConfig?>(null);



  final List<ModelConfig> _defaultModels = [
    // 添加 closeAI 模型作为默认模型
    ModelConfig(
      name: 'closeAI',
      apiKey: '', // 留空，用户在设置页面填写
      apiUrl: 'https://api.openai-proxy.org',
      apiPath: '/v1/chat/completions',
      model: 'gpt-4.1-2025-04-14',
      modelVariants: [
        'gpt-4.1-2025-04-14',
      ],
      apiFormat: 'OpenAI API兼容',
      appId: '',
      maxTokens: 6000,
    ),
    // 添加阿里云通义千问模型
    ModelConfig(
      name: '阿里云通义千问',
      apiKey: '', // 留空，用户在设置页面填写
      apiUrl: 'https://dashscope.aliyuncs.com',
      apiPath: '/compatible-mode/v1/chat/completions',
      model: 'qwen3-235b-a22b',
      modelVariants: [
        'qwen3-235b-a22b',
      ],
      apiFormat: 'OpenAI API兼容',
      appId: '',
      maxTokens: 6000,
      enableThinking: false, // 默认不启用深度思考模式
    ),
    // 添加中转站模型
    ModelConfig(
      name: '中转站',
      apiKey: '', // 留空，用户在设置页面填写
      apiUrl: 'https://dzwm.xyz',
      apiPath: '/v1/chat/completions',
      model: 'gpt-4.1-mini-2025-04-14',
      modelVariants: [
        'gpt-4.1-mini-2025-04-14',
      ],
      apiFormat: 'OpenAI API兼容',
      appId: '',
      maxTokens: 6000,
    ),
    ModelConfig(
      name: 'ChatGPT',
      apiKey: '',
      apiUrl: 'https://api.openai.com',
      apiPath: '/v1/chat/completions',
      model: 'gpt-4',
      apiFormat: 'OpenAI API兼容',
    ),
    ModelConfig(
      name: '硅基流动',
      apiKey: '',
      apiUrl: 'https://api.siliconflow.cn',
      apiPath: '/v1/chat/completions',
      model: 'deepseek-ai/DeepSeek-V3',
      apiFormat: 'OpenAI API兼容',
    ),
    ModelConfig(
      name: 'Deepseek',
      apiKey: '',
      apiUrl: 'https://api.deepseek.com',
      apiPath: '/v1/chat/completions',
      model: 'deepseek-chat',
      apiFormat: 'OpenAI API兼容',
    ),
    ModelConfig(
      name: '阿里百炼（通义）',
      apiKey: '',
      apiUrl: 'https://dashscope.aliyuncs.com',
      apiPath: '/compatible-mode/v1/chat/completions',
      model: 'qwen-turbo-2024-11-01',
      modelVariants: [
        'qwen2.5-7b-instruct-1m', // 阿里云比较好用的模型
        'deepseek-r1', // 阿里云的deepseek标识符
        'qwq-32b', // 阿里云推理模型
        'qwq-plus-2025-03-05', // 阿里云推理模型plus
        'qwen3-235b-a22b', // 阿里云大模型
      ],
      apiFormat: 'OpenAI API兼容',
    ),
    ModelConfig(
      name: '火山引擎（豆包）',
      apiKey: '',
      apiUrl: 'https://ork.cn-beijing.volces.com',
      apiPath: '/api/v3/chat/completions',
      model: 'doupo-1-5-pro-256k-250115',
      apiFormat: 'OpenAI API兼容',
    ),
    ModelConfig(
      name: '百度千帆',
      apiKey: '',
      apiUrl: 'https://qianfan.baidubce.com',
      apiPath: '/v2/chat/completions',
      model: 'deepseek-v3',
      apiFormat: 'OpenAI API兼容',
      appId: '',
    ),
    ModelConfig(
      name: 'Gemini',
      apiKey: '',
      apiUrl: 'https://generativelanguage.googleapis.com',
      apiPath: '/v1beta/models/gemini-1.5-flash:generateContent',
      model: 'gemini-1.5-flash',
      apiFormat: 'Google API',
      modelVariants: [
        'gemini-1.5-flash',
        'gemini-1.5-pro',
        'gemini-2.0-flash-exp',
      ],
      useProxy: false,
      proxyUrl: '',
      timeout: 120, // 设置更长的超时时间
    ),
  ];

  final _storage = GetStorage();
  final apiKey = ''.obs;
  final baseUrl = ''.obs;
  final ttsApiKey = ''.obs;
  final isTextToSpeechMode = false.obs;

  static const String _apiKeyKey = 'api_key';
  static const String _baseUrlKey = 'base_url';
  static const String _ttsApiKeyKey = 'tts_api_key';
  static const String _configModeKey = 'config_mode';
  static const String _lastUpdateCheckKey = 'last_update_check';

  final Rx<ModelConfig> currentModel = ModelConfig(
    name: 'default',
    apiKey: '',
    apiUrl: '',
    apiPath: '/v1/chat/completions', // 默认使用OpenAI格式的路径
    model: 'gpt-3.5-turbo',
    apiFormat: 'openai',
    maxTokens: 7000,
    temperature: 0.7,
  ).obs;

  @override
  void onInit() async {
    super.onInit();
    await _initializeBox();
  }

  Future<void> _initializeBox() async {
    _box = await Hive.openBox(_boxName);
    _loadModels();
    _loadConfig();
    // 在所有配置加载后执行修复
    Future.delayed(const Duration(milliseconds: 100), () {
      _checkAndFixModelNames();
    });
  }

  void _loadModels() {
    // 加载默认模型
    models.addAll(_defaultModels);

    // 加载自定义模型
    final savedCustomModels = _box.get(_customModelsKey);
    if (savedCustomModels != null) {
      final customModels = (savedCustomModels as List)
          .map((e) => ModelConfig.fromJson(Map<String, dynamic>.from(e)))
          .where((model) => model.isCustom)
          .toList();
      models.addAll(customModels);
    }

    // 加载每个模型的配置
    for (var i = 0; i < models.length; i++) {
      final savedConfig = _box.get(models[i].name);
      if (savedConfig != null) {
        models[i] =
            ModelConfig.fromJson(Map<String, dynamic>.from(savedConfig));
      }
    }

    // 先检查是否有重复的中转站模型
    _checkDuplicateTransferStation();

    // 确保模型名称唯一性
    _ensureUniqueModelNames();
  }

  // 检查是否有重复的中转站模型
  void _checkDuplicateTransferStation() {
    // 计算中转站模型的数量
    int transferStationCount = 0;
    for (final model in models) {
      if (model.name == '中转站') {
        transferStationCount++;
      }
    }

    // 如果有多个中转站模型，只保留第一个
    if (transferStationCount > 1) {
      bool foundFirst = false;
      models.removeWhere((model) {
        if (model.name == '中转站') {
          if (!foundFirst) {
            foundFirst = true;
            return false; // 保留第一个
          }
          return true; // 移除其他的
        }
        return false; // 保留非中转站模型
      });
    }
  }

  // 确保模型名称唯一性
  void _ensureUniqueModelNames() {
    // 创建一个集合来跟踪已经处理过的模型名称
    final Set<String> processedNames = {};
    final List<ModelConfig> uniqueModels = [];

    for (final model in models) {
      if (processedNames.contains(model.name)) {
        // 如果名称已存在，为自定义模型添加后缀
        if (model.isCustom) {
          var newName = '${model.name} (自定义)';
          var counter = 1;
          // 如果添加后缀后仍然重复，则添加数字
          while (processedNames.contains(newName)) {
            counter++;
            newName = '${model.name} (自定义 $counter)';
          }
          model.name = newName;
        } else {
          // 如果是默认模型重复，跳过这个模型
          continue;
        }
      }

      processedNames.add(model.name);
      uniqueModels.add(model);
    }

    // 更新模型列表
    models.clear();
    models.addAll(uniqueModels);
  }

  void _updateCurrentModelConfig() {
    final config = getCurrentModel();
    temperature.value = config.temperature;
    topP.value = config.topP;
    maxTokens.value = config.maxTokens;
    repetitionPenalty.value = config.repetitionPenalty; // 更新重复惩罚参数

    // 重要：同步更新 currentModel 响应式变量，确保模型切换立即生效
    currentModel.value = config;
  }

  ModelConfig getCurrentModel() {
    if (selectedModelId.value.isEmpty) {
      if (models.isNotEmpty) {
        selectedModelId.value = models[0].name;
      } else {
        throw Exception('未找到可用的模型');
      }
    }

    return models.firstWhere(
      (model) => model.name == selectedModelId.value,
      orElse: () => models.isNotEmpty ? models[0] : throw Exception('未找到可用的模型'),
    );
  }

  void updateSelectedModel(String modelName) {
    selectedModelId.value = modelName;
    // 保存选择的模型ID到Hive存储中，确保应用重启后能恢复选择
    _box.put('selected_model', modelName);
    _updateCurrentModelConfig();

    // 立即通知所有监听者模型已切换
    print('模型已切换到: $modelName');
  }

  void updateTemperature(double value) {
    temperature.value = value;
    _saveCurrentConfig();
  }

  void updateTopP(double value) {
    topP.value = value;
    _saveCurrentConfig();
  }

  void updateMaxTokens(int value) {
    maxTokens.value = value;
    currentModel.update((model) {
      model?.maxTokens = value;
    });
    // 同步更新到models中
    final index = models.indexWhere((m) => m.name == selectedModelId.value);
    if (index != -1) {
      models[index] = models[index].copyWith(maxTokens: value);
      _box.put(selectedModelId.value, models[index].toJson());
    }
  }

  void updateRepetitionPenalty(double value) {
    repetitionPenalty.value = value;
    _saveCurrentConfig();
  }

  Future<void> addCustomModel(ModelConfig model) async {
    model.isCustom = true;
    models.add(model);
    await _saveCustomModels();
    await _box.put(model.name, model.toJson());
  }

  Future<void> removeCustomModel(String modelName) async {
    models.removeWhere((m) => m.name == modelName && m.isCustom);
    await _saveCustomModels();
    await _box.delete(modelName);
    if (selectedModelId.value == modelName) {
      selectedModelId.value = models.first.name;
      // 保存选择的模型ID到Hive存储中
      _box.put('selected_model', selectedModelId.value);
      _updateCurrentModelConfig();
      print('删除自定义模型后，已切换到: ${selectedModelId.value}');
    }
  }

  Future<void> updateModelConfig(
    String modelName, {
    String? apiKey,
    String? apiUrl,
    String? apiPath,
    String? model,
    List<String>? modelVariants,
    String? apiFormat,
    String? appId,
    int? maxTokens,
    bool? useProxy,
    String? proxyUrl,
    int? timeout,
    bool? enableThinking,
  }) async {
    final index = models.indexWhere((m) => m.name == modelName);
    if (index != -1) {
      models[index] = models[index].copyWith(
        apiKey: apiKey,
        apiUrl: apiUrl,
        apiPath: apiPath,
        model: model,
        modelVariants: modelVariants,
        apiFormat: apiFormat,
        appId: appId,
        maxTokens: maxTokens,
        useProxy: useProxy,
        proxyUrl: proxyUrl,
        timeout: timeout,
        enableThinking: enableThinking,
      );
      await _box.put(modelName, models[index].toJson());

      // 如果是当前选中的模型，同步更新currentModel
      if (modelName == selectedModelId.value) {
        currentModel.update((model) {
          if (model != null) {
            if (maxTokens != null) model.maxTokens = maxTokens;
            if (apiKey != null) model.apiKey = apiKey;
            if (apiUrl != null) model.apiUrl = apiUrl;
            if (apiPath != null) model.apiPath = apiPath;
            if (apiFormat != null) model.apiFormat = apiFormat;
            if (appId != null) model.appId = appId;
            if (useProxy != null) model.useProxy = useProxy;
            if (proxyUrl != null) model.proxyUrl = proxyUrl;
            if (timeout != null) model.timeout = timeout;
            if (enableThinking != null) model.enableThinking = enableThinking;
          }
        });
      }
    }
  }

  Future<void> _saveCustomModels() async {
    final customModels =
        models.where((m) => m.isCustom).map((m) => m.toJson()).toList();
    await _box.put(_customModelsKey, customModels);
  }

  Future<void> _saveCurrentConfig() async {
    final index = models.indexWhere((m) => m.name == selectedModelId.value);
    if (index != -1) {
      models[index] = models[index].copyWith(
        temperature: temperature.value,
        topP: topP.value,
        maxTokens: maxTokens.value,
        repetitionPenalty: repetitionPenalty.value, // 保存重复惩罚参数
      );
      await _box.put(selectedModelId.value, models[index].toJson());
    }
  }

  // 重置为默认配置
  Future<void> resetToDefaults() async {
    // 删除所有自定义模型
    final customModels = models.where((m) => m.isCustom).toList();
    for (final model in customModels) {
      await _box.delete(model.name);
    }
    models.removeWhere((m) => m.isCustom);
    await _box.delete(_customModelsKey);

    // 重置默认模型配置
    models.clear();
    models.addAll(_defaultModels);
    selectedModelId.value = models[0].name;
    // 保存选择的模型ID到Hive存储中
    _box.put('selected_model', selectedModelId.value);
    _updateCurrentModelConfig();
    print('已重置为默认配置，当前模型: ${selectedModelId.value}');
  }

  void _loadConfig() {
    // 加载配置
    final savedModelId = _box.get('selected_model');
    if (savedModelId != null) {
      // 检查保存的模型ID是否存在于当前模型列表中
      bool modelExists = models.any((model) => model.name == savedModelId);
      if (modelExists) {
        selectedModelId.value = savedModelId;
      } else if (models.isNotEmpty) {
        // 如果保存的模型不存在，使用第一个可用的模型
        selectedModelId.value = models[0].name;
        // 更新保存的模型ID
        _box.put('selected_model', selectedModelId.value);
      }
    } else if (models.isNotEmpty) {
      selectedModelId.value = models[0].name;
      // 保存默认选择的模型ID
      _box.put('selected_model', selectedModelId.value);
    }

    // 修复模型名称更改问题
    if (selectedModelId.value == '通义千问') {
      selectedModelId.value = '阿里百炼（通义）';
      _box.put('selected_model', selectedModelId.value);
    }

    // 加载嵌入模型配置
    final savedEmbeddingModel = _box.get(_embeddingModelKey);
    if (savedEmbeddingModel != null) {
      embeddingModel.value = EmbeddingModelConfig.fromJson(
          Map<String, dynamic>.from(savedEmbeddingModel));
    }

    // 加载Apply模型配置
    final savedApplyModel = _box.get(_applyModelKey);
    if (savedApplyModel != null) {
      try {
        final applyModelConfig = ModelConfig.fromJson(
            Map<String, dynamic>.from(savedApplyModel));

        // 验证保存的Apply模型是否仍然存在于当前模型列表中
        final existingModel = models.where((m) => m.name == applyModelConfig.name).firstOrNull;
        if (existingModel != null) {
          applyModel.value = existingModel;
        } else {
          // 如果保存的模型不存在，尝试找到推荐的Apply模型
          applyModel.value = _findRecommendedApplyModel();
        }
      } catch (e) {
        print('加载Apply模型配置失败: $e');
        applyModel.value = _findRecommendedApplyModel();
      }
    } else {
      // 如果没有保存的Apply模型，自动选择推荐的模型
      applyModel.value = _findRecommendedApplyModel();
    }

    _updateCurrentModelConfig();
  }

  void setApiKey(String value) {
    apiKey.value = value;
    _storage.write(_apiKeyKey, value);
  }

  void setBaseUrl(String value) {
    baseUrl.value = value;
    _storage.write(_baseUrlKey, value);
  }

  void setTTSApiKey(String value) {
    ttsApiKey.value = value;
    _storage.write(_ttsApiKeyKey, value);
  }

  void toggleConfigMode() {
    isTextToSpeechMode.value = !isTextToSpeechMode.value;
    _storage.write(_configModeKey, isTextToSpeechMode.value);
  }

  /// 更新嵌入模型配置
  Future<void> updateEmbeddingModel({
    String? name,
    String? apiKey,
    String? baseUrl,
    String? apiPath,
    String? modelName,
    String? apiFormat,
    int? topK,
    bool? enabled,
    bool? useProxy,
    String? proxyUrl,
    int? timeout,
    int? maxChunkSize,
    double? similarityThreshold,
  }) async {
    embeddingModel.update((model) {
      if (model != null) {
        if (name != null) model.name = name;
        if (apiKey != null) model.apiKey = apiKey;
        if (baseUrl != null) model.baseUrl = baseUrl;
        if (apiPath != null) model.apiPath = apiPath;
        if (modelName != null) model.modelName = modelName;
        if (apiFormat != null) model.apiFormat = apiFormat;
        if (topK != null) model.topK = topK;
        if (enabled != null) model.enabled = enabled;
        if (useProxy != null) model.useProxy = useProxy;
        if (proxyUrl != null) model.proxyUrl = proxyUrl;
        if (timeout != null) model.timeout = timeout;
        if (maxChunkSize != null) model.maxChunkSize = maxChunkSize;
        if (similarityThreshold != null)
          model.similarityThreshold = similarityThreshold;
      }
    });

    await _saveEmbeddingModelConfig();
  }

  /// 验证嵌入模型连接
  Future<Map<String, dynamic>> validateEmbeddingConnection() async {
    try {
      // 获取EmbeddingService实例
      final embeddingService = Get.find<EmbeddingService>();

      // 验证连接
      final result =
          await embeddingService.validateConnection(embeddingModel.value);

      return result;
    } catch (e) {
      print('验证嵌入模型连接失败: $e');
      return {
        'success': false,
        'message': '验证失败: ${e.toString()}',
      };
    }
  }

  /// 验证模型连接
  Future<Map<String, dynamic>> validateModelConnection(String modelName) async {
    try {
      final model = models.firstWhere(
        (m) => m.name == modelName,
        orElse: () => throw Exception('未找到模型: $modelName'),
      );

      // 如果是Google API，确保路径正确
      if (model.apiFormat == 'Google API' &&
          !model.apiPath.contains(':generateContent')) {
        // 自动修正路径
        final correctPath = '/v1beta/models/${model.model}:generateContent';
        await updateModelConfig(model.name, apiPath: correctPath);

        return {
          'success': true,
          'message': '路径已自动修正为: $correctPath',
          'fixed': true,
        };
      }

      // 构建测试URL
      String url;
      if (model.apiFormat == 'Google API') {
        url = '${model.apiUrl}${model.apiPath}?key=${model.apiKey}';
      } else {
        url = '${model.apiUrl}${model.apiPath}';
      }

      // 首先测试基本连接
      Map<String, dynamic> connectionResult;
      if (model.useProxy && model.proxyUrl.isNotEmpty) {
        // 使用代理进行测试
        connectionResult = await _validateWithProxy(model);
      } else {
        // 使用系统默认网络进行测试（支持VPN）
        connectionResult = await NetworkClient.testConnection(
          url: model.apiUrl,
          timeout: Duration(seconds: model.timeout > 0 ? model.timeout : 30),
          useProxy: false,
        );
      }

      // 如果基本连接成功，测试模型生成能力
      if (connectionResult['success']) {
        try {
          // 使用AI服务测试生成能力
          final aiService = Get.find<AIService>();
          final testResult = await aiService.testModelGeneration(model);

          return {
            'success': true,
            'message': '模型连接和生成测试成功',
            'connectionTest': connectionResult,
            'generationTest': testResult,
            'fullTest': true,
          };
        } catch (e) {
          return {
            'success': false,
            'message': '连接成功，但模型生成测试失败: ${e.toString()}',
            'connectionTest': connectionResult,
            'error': e.toString(),
          };
        }
      }

      // 如果基本连接失败，直接返回结果
      return connectionResult;
    } catch (e) {
      print('验证模型连接失败: $e');
      return {
        'success': false,
        'message': '验证失败: ${e.toString()}',
      };
    }
  }

  /// 使用代理验证连接
  Future<Map<String, dynamic>> _validateWithProxy(ModelConfig model) async {
    try {
      // 解析代理地址和端口
      final parts = model.proxyUrl.split(':');
      if (parts.length != 2) {
        throw Exception('代理地址格式错误，应为 host:port，如 127.0.0.1:7890');
      }

      final proxyHost = parts[0];
      final proxyPort = int.tryParse(parts[1]);
      if (proxyPort == null) {
        throw Exception('代理端口格式错误: ${parts[1]}');
      }

      // 创建一个新的HttpClient并设置代理
      final httpClient = HttpClient();
      httpClient.findProxy = (uri) => 'PROXY $proxyHost:$proxyPort';

      // 设置超时
      final timeout = Duration(seconds: model.timeout > 0 ? model.timeout : 30);
      httpClient.connectionTimeout = timeout;

      try {
        // 发送请求
        final request = await httpClient.getUrl(Uri.parse(model.apiUrl));
        request.headers.add('Accept', 'application/json');

        // 等待响应
        final response = await request.close().timeout(timeout);

        // 读取一些响应数据，但不必完全读取
        // 这样可以避免内容长度相关的问题
        final buffer = StringBuffer();
        try {
          await response
              .transform(utf8.decoder)
              .listen((data) {
                buffer.write(data);
              })
              .asFuture()
              .timeout(const Duration(seconds: 2), onTimeout: () {
                // 超时后不再等待更多数据
                return;
              });
        } catch (e) {
          // 忽略读取数据时的错误
        }

        return {
          'success': response.statusCode < 500,
          'message': '使用代理连接测试成功，状态码: ${response.statusCode}',
          'statusCode': response.statusCode,
          'proxy': true,
        };
      } finally {
        httpClient.close();
      }
    } catch (e) {
      print('使用代理验证连接失败: $e');
      return {
        'success': false,
        'message': '使用代理验证失败: ${e.toString()}',
        'proxy': true,
      };
    }
  }

  /// 保存嵌入模型配置
  Future<void> _saveEmbeddingModelConfig() async {
    await _box.put(_embeddingModelKey, embeddingModel.value.toJson());
  }

  /// 保存Apply模型配置
  Future<void> saveApplyModelConfig() async {
    if (applyModel.value != null) {
      await _box.put(_applyModelKey, applyModel.value!.toJson());
    }
  }

  /// 设置Apply模型
  Future<void> setApplyModel(ModelConfig model) async {
    applyModel.value = model;
    await saveApplyModelConfig();
  }

  /// 获取可用的Apply模型列表（从现有模型列表中获取）
  List<ModelConfig> getAvailableApplyModels() {
    if (models.isEmpty) {
      return [];
    }

    // 返回所有已配置的模型，但按推荐程度排序
    final sortedModels = List<ModelConfig>.from(models);

    // 按推荐程度排序：推荐的模型排在前面
    sortedModels.sort((a, b) {
      final aIsRecommended = _isRecommendedApplyModel(a);
      final bIsRecommended = _isRecommendedApplyModel(b);

      if (aIsRecommended && !bIsRecommended) return -1;
      if (!aIsRecommended && bIsRecommended) return 1;
      return a.name.compareTo(b.name);
    });

    return sortedModels;
  }

  /// 判断是否为推荐的Apply模型
  bool _isRecommendedApplyModel(ModelConfig model) {
    final recommendedKeywords = [
      'gpt-3.5',
      'claude-3-haiku',
      'claude-haiku',
      'qwen-turbo',
      'gemini-flash',
      'gemini-1.5-flash',
      'llama-3.1-8b',
      'llama-3.2-8b',
      'mistral-7b',
      'yi-lightning',
      'deepseek-chat',
    ];

    final lowerModelName = model.model.toLowerCase();
    final lowerDisplayName = model.name.toLowerCase();

    return recommendedKeywords.any((keyword) =>
        lowerModelName.contains(keyword) || lowerDisplayName.contains(keyword));
  }

  /// 查找推荐的Apply模型
  ModelConfig? _findRecommendedApplyModel() {
    if (models.isEmpty) return null;

    // 首先尝试找到推荐的模型
    for (final model in models) {
      if (_isRecommendedApplyModel(model)) {
        return model;
      }
    }

    // 如果没有推荐的模型，返回第一个可用模型
    return models.first;
  }

  /// 获取Apply模型的推荐等级
  ApplyModelRecommendation getApplyModelRecommendation(ModelConfig model) {
    if (_isRecommendedApplyModel(model)) {
      return ApplyModelRecommendation.recommended;
    }

    // 检查是否为大型模型（可能较慢）
    final largeModelKeywords = [
      'gpt-4',
      'claude-3-opus',
      'claude-3-sonnet',
      'gemini-pro',
      'llama-3.1-70b',
      'llama-3.1-405b',
    ];

    final lowerModelName = model.model.toLowerCase();
    final isLargeModel = largeModelKeywords.any((keyword) =>
        lowerModelName.contains(keyword));

    if (isLargeModel) {
      return ApplyModelRecommendation.notRecommended;
    }

    return ApplyModelRecommendation.acceptable;
  }

  /// 获取Apply模型的优化配置
  ModelConfig getOptimizedApplyModelConfig(ModelConfig baseModel) {
    return baseModel.copyWith(
      temperature: 0.1,  // 低温度确保一致性
      maxTokens: 4000,   // 适中的token限制
      topP: 0.9,         // 稍微降低随机性
    );
  }

  // 添加模型变体到指定模型
  Future<void> addModelVariant(String modelName, String variant) async {
    if (variant.isEmpty) return;

    final index = models.indexWhere((m) => m.name == modelName);
    if (index != -1) {
      // 添加变体到模型
      models[index].addModelVariant(variant);
      await _box.put(modelName, models[index].toJson());

      // 如果是当前选中的模型，更新当前模型配置
      if (modelName == selectedModelId.value) {
        _updateCurrentModelConfig();
      }
    }
  }

  // 删除指定模型的变体
  Future<void> removeModelVariant(String modelName, String variant) async {
    final index = models.indexWhere((m) => m.name == modelName);
    if (index != -1) {
      // 删除变体
      models[index].removeModelVariant(variant);
      await _box.put(modelName, models[index].toJson());

      // 如果当前模型正在使用这个变体，切换回主模型标识符
      if (modelName == selectedModelId.value &&
          models[index].model == variant) {
        // 重置为主模型标识符
        final mainModel = models[index].model;
        updateModelIdentifier(modelName, mainModel);
      }
    }
  }

  // 切换到指定模型的指定变体
  Future<void> switchToModelVariant(String modelName, String variant) async {
    final index = models.indexWhere((m) => m.name == modelName);
    if (index != -1 && models[index].modelVariants.contains(variant)) {
      // 切换模型标识符
      updateModelIdentifier(modelName, variant);
    }
  }

  // 更新模型标识符
  Future<void> updateModelIdentifier(
      String modelName, String newIdentifier) async {
    final index = models.indexWhere((m) => m.name == modelName);
    if (index != -1) {
      models[index] = models[index].copyWith(model: newIdentifier);
      await _box.put(modelName, models[index].toJson());

      // 如果是当前选中的模型，完全同步更新currentModel
      if (modelName == selectedModelId.value) {
        currentModel.value = models[index];
        print('模型标识符已更新为: $newIdentifier');
      }
    }
  }

  // 获取指定模型的所有变体
  List<String> getModelVariants(String modelName) {
    final index = models.indexWhere((m) => m.name == modelName);
    if (index != -1) {
      return [...models[index].modelVariants];
    }
    return [];
  }

  // 获取当前选中模型的所有变体
  List<String> getCurrentModelVariants() {
    return getModelVariants(selectedModelId.value);
  }

  // 添加一个检查并修复模型名称更改的函数
  void _checkAndFixModelNames() {
    // 检查当前选择的模型
    if (selectedModelId.value == '通义千问') {
      selectedModelId.value = '阿里百炼（通义）';
      _box.put('selected_model', selectedModelId.value);
    }
  }
}

/// Apply模型推荐等级
enum ApplyModelRecommendation {
  /// 推荐使用（轻量级、快速响应）
  recommended,
  /// 可接受（中等性能）
  acceptable,
  /// 不推荐（大型模型，可能较慢）
  notRecommended,
}
